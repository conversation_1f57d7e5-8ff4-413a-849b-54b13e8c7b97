<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>সমস্যা সমাধান পরীক্ষা</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-title {
            color: #2c3e50;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
            margin-bottom: 15px;
        }
        .test-button {
            background: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #2980b9;
        }
        .success {
            color: #27ae60;
            font-weight: bold;
        }
        .error {
            color: #e74c3c;
            font-weight: bold;
        }
        .info {
            color: #3498db;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <h1>নোটিফিকেশন এবং এডিট মডাল সমস্যা সমাধান পরীক্ষা</h1>
    
    <div class="test-container">
        <h2 class="test-title">১. নোটিফিকেশন প্যানেলে ট্রান্সজিকশন আইডি পরীক্ষা</h2>
        <p>এই পরীক্ষাটি নোটিফিকেশন প্যানেলে ট্রান্সজিকশন আইডি দেখাচ্ছে কিনা তা যাচাই করবে।</p>
        <button class="test-button" onclick="testNotificationWithTransactionId()">নোটিফিকেশন আইডি পরীক্ষা</button>
        <div id="notification-test-result"></div>
    </div>

    <div class="test-container">
        <h2 class="test-title">২. নতুন সার্চেবল ক্যাটেগরি ড্রপডাউন পরীক্ষা</h2>
        <p>এই পরীক্ষাটি নতুন সার্চেবল ক্যাটেগরি ড্রপডাউন এবং সঠিক ক্যাটেগরি নির্বাচন যাচাই করবে।</p>
        <button class="test-button" onclick="testSearchableDropdown()">সার্চেবল ড্রপডাউন পরীক্ষা</button>
        <button class="test-button" onclick="testQuickCategoryFix()">দ্রুত ক্যাটেগরি সমস্যা পরীক্ষা</button>
        <div id="edit-modal-test-result"></div>
    </div>

    <div class="test-container">
        <h2 class="test-title">৩. সম্পূর্ণ ওয়ার্কফ্লো পরীক্ষা</h2>
        <p>এই পরীক্ষাটি সম্পূর্ণ ওয়ার্কফ্লো (আয় যোগ → এডিট → নোটিফিকেশন) পরীক্ষা করবে।</p>
        <button class="test-button" onclick="testCompleteWorkflow()">সম্পূর্ণ ওয়ার্কফ্লো পরীক্ষা</button>
        <div id="workflow-test-result"></div>
    </div>

    <script>
        // নোটিফিকেশন আইডি পরীক্ষা
        function testNotificationWithTransactionId() {
            const resultDiv = document.getElementById('notification-test-result');
            resultDiv.innerHTML = '<p class="info">পরীক্ষা চলছে...</p>';
            
            try {
                // মূল অ্যাপ্লিকেশনে নোটিফিকেশন যোগ করার চেষ্টা
                if (window.parent && window.parent.moneyManager) {
                    const testNotification = {
                        title: 'পরীক্ষা নোটিফিকেশন',
                        message: 'এটি একটি পরীক্ষা নোটিফিকেশন যাতে ট্রান্সজিকশন আইডি রয়েছে',
                        type: 'transaction',
                        category: 'transactions',
                        actionData: {
                            transactionId: 'test-id-12345',
                            transactionType: 'income',
                            action: 'edit'
                        }
                    };
                    
                    window.parent.moneyManager.addNotification(
                        testNotification.title,
                        testNotification.message,
                        testNotification.type,
                        testNotification.category,
                        testNotification.actionData
                    );
                    
                    resultDiv.innerHTML = '<p class="success">✓ নোটিফিকেশন সফলভাবে যোগ করা হয়েছে। নোটিফিকেশন প্যানেল চেক করুন।</p>';
                } else {
                    resultDiv.innerHTML = '<p class="error">✗ মূল অ্যাপ্লিকেশন পাওয়া যায়নি। index.html থেকে এই পরীক্ষা চালান।</p>';
                }
            } catch (error) {
                resultDiv.innerHTML = `<p class="error">✗ ত্রুটি: ${error.message}</p>`;
            }
        }

        // এডিট মডাল ক্যাটেগরি পরীক্ষা
        function testEditModalCategory() {
            const resultDiv = document.getElementById('edit-modal-test-result');
            resultDiv.innerHTML = '<p class="info">পরীক্ষা চলছে...</p>';

            try {
                if (window.parent && window.parent.moneyManager && window.parent.testCategorySelection) {
                    // নতুন উন্নত টেস্ট ফাংশন ব্যবহার করি
                    window.parent.testCategorySelection();

                    resultDiv.innerHTML = `
                        <div class="success">
                            <p>✓ বিস্তারিত ক্যাটেগরি পরীক্ষা শুরু হয়েছে।</p>
                            <p><strong>ব্রাউজার কনসোল (F12) চেক করুন বিস্তারিত ফলাফলের জন্য।</strong></p>
                            <p>পরীক্ষা সম্পন্ন হতে প্রায় ১২ সেকেন্ড সময় লাগবে।</p>
                        </div>
                    `;
                } else {
                    // ফলব্যাক: সাধারণ পরীক্ষা
                    const testIncome = {
                        id: 'test-income-' + Date.now(),
                        amount: 5000,
                        category: 'salary',
                        description: 'পরীক্ষা আয়',
                        date: new Date().toISOString().split('T')[0],
                        time: new Date().toLocaleTimeString('bn-BD'),
                        createdAt: new Date().toISOString(),
                        updatedAt: new Date().toISOString()
                    };

                    // ডেটায় যোগ করি
                    window.parent.moneyManager.data.income.push(testIncome);
                    window.parent.moneyManager.saveData();

                    // এডিট মডাল খুলি
                    setTimeout(() => {
                        window.parent.moneyManager.editTransaction(testIncome.id, 'income');

                        // ক্যাটেগরি চেক করি
                        setTimeout(() => {
                            const categorySelect = window.parent.document.getElementById('editCategory');
                            if (categorySelect && categorySelect.value === 'salary') {
                                resultDiv.innerHTML = '<p class="success">✓ এডিট মডালে সঠিক ক্যাটেগরি নির্বাচিত হয়েছে।</p>';
                            } else {
                                resultDiv.innerHTML = `<p class="error">✗ ক্যাটেগরি সঠিক নয়। প্রত্যাশিত: salary, পাওয়া: ${categorySelect ? categorySelect.value : 'undefined'}</p>`;
                            }
                        }, 500);
                    }, 100);
                }
            } catch (error) {
                resultDiv.innerHTML = `<p class="error">✗ ত্রুটি: ${error.message}</p>`;
            }
        }

        // সার্চেবল ড্রপডাউন পরীক্ষা
        function testSearchableDropdown() {
            const resultDiv = document.getElementById('edit-modal-test-result');
            resultDiv.innerHTML = '<p class="info">সার্চেবল ড্রপডাউন পরীক্ষা চলছে...</p>';

            try {
                if (window.parent && window.parent.moneyManager) {
                    // বিভিন্ন ক্যাটেগরি দিয়ে টেস্ট
                    const testCases = [
                        {category: 'business', type: 'income', expectedText: 'ব্যবসা'},
                        {category: 'food', type: 'expense', expectedText: 'খাবার'},
                        {category: 'salary', type: 'income', expectedText: 'বেতন'},
                        {category: 'transport', type: 'expense', expectedText: 'যাতায়াত'}
                    ];

                    let currentTest = 0;
                    const results = [];

                    const runNextTest = () => {
                        if (currentTest >= testCases.length) {
                            // সব টেস্ট শেষ
                            const passedTests = results.filter(r => r.passed).length;
                            const totalTests = results.length;

                            resultDiv.innerHTML = `
                                <div class="${passedTests === totalTests ? 'success' : 'error'}">
                                    <h4>সার্চেবল ড্রপডাউন পরীক্ষার ফলাফল:</h4>
                                    <p><strong>পাস:</strong> ${passedTests}/${totalTests}</p>
                                    <ul>
                                        ${results.map(r => `
                                            <li class="${r.passed ? 'success' : 'error'}">
                                                ${r.passed ? '✓' : '✗'} ${r.category} (${r.type}): ${r.message}
                                            </li>
                                        `).join('')}
                                    </ul>
                                    ${passedTests === totalTests ?
                                        '<p><strong>🎉 সব পরীক্ষা সফল! সার্চেবল ড্রপডাউন সঠিকভাবে কাজ করছে।</strong></p>' :
                                        '<p><strong>⚠️ কিছু পরীক্ষা ব্যর্থ হয়েছে। ব্রাউজার কনসোল চেক করুন।</strong></p>'
                                    }
                                </div>
                            `;
                            return;
                        }

                        const testCase = testCases[currentTest];
                        console.log(`Running test ${currentTest + 1}: ${testCase.category} (${testCase.type})`);

                        // টেস্ট ডেটা তৈরি
                        const testData = {
                            id: 'searchable-test-' + Date.now() + '-' + currentTest,
                            amount: 1000 + (currentTest * 500),
                            category: testCase.category,
                            description: `সার্চেবল টেস্ট: ${testCase.category}`,
                            date: new Date().toISOString().split('T')[0],
                            time: new Date().toLocaleTimeString('bn-BD'),
                            createdAt: new Date().toISOString(),
                            updatedAt: new Date().toISOString()
                        };

                        // ডেটায় যোগ করি
                        window.parent.moneyManager.data[testCase.type].push(testData);
                        window.parent.moneyManager.saveData();

                        // এডিট মডাল খুলি
                        setTimeout(() => {
                            window.parent.moneyManager.editTransaction(testData.id, testCase.type);

                            // ড্রপডাউন চেক করি
                            setTimeout(() => {
                                const searchInput = window.parent.document.getElementById('editCategorySearch');
                                const hiddenInput = window.parent.document.getElementById('editCategory');

                                const passed = searchInput && hiddenInput &&
                                              searchInput.value === testCase.expectedText &&
                                              hiddenInput.value === testCase.category;

                                results.push({
                                    category: testCase.category,
                                    type: testCase.type,
                                    passed: passed,
                                    message: passed ?
                                        `সঠিক: "${testCase.expectedText}"` :
                                        `ভুল: প্রত্যাশিত "${testCase.expectedText}", পাওয়া "${searchInput ? searchInput.value : 'undefined'}"`
                                });

                                console.log(`Test ${currentTest + 1} result:`, {
                                    expected: testCase.expectedText,
                                    actualSearch: searchInput ? searchInput.value : 'undefined',
                                    actualHidden: hiddenInput ? hiddenInput.value : 'undefined',
                                    passed: passed
                                });

                                // মডাল বন্ধ করি
                                const modal = window.parent.document.getElementById('editModal');
                                if (modal) modal.style.display = 'none';

                                currentTest++;
                                setTimeout(runNextTest, 500);
                            }, 800);
                        }, 300);
                    };

                    runNextTest();
                } else {
                    resultDiv.innerHTML = '<p class="error">✗ মূল অ্যাপ্লিকেশন পাওয়া যায়নি।</p>';
                }
            } catch (error) {
                resultDiv.innerHTML = `<p class="error">✗ ত্রুটি: ${error.message}</p>`;
                console.error('Searchable dropdown test error:', error);
            }
        }

        // দ্রুত ক্যাটেগরি সমস্যা পরীক্ষা
        function testQuickCategoryFix() {
            const resultDiv = document.getElementById('edit-modal-test-result');
            resultDiv.innerHTML = '<p class="info">দ্রুত পরীক্ষা চলছে...</p>';

            try {
                if (window.parent && window.parent.moneyManager) {
                    // একটি নির্দিষ্ট ক্যাটেগরি দিয়ে টেস্ট
                    const testData = {
                        id: 'quick-test-' + Date.now(),
                        amount: 3000,
                        category: 'business', // এই ক্যাটেগরিটি নির্বাচিত হওয়া উচিত
                        description: 'দ্রুত ক্যাটেগরি পরীক্ষা',
                        date: new Date().toISOString().split('T')[0],
                        time: new Date().toLocaleTimeString('bn-BD'),
                        createdAt: new Date().toISOString(),
                        updatedAt: new Date().toISOString()
                    };

                    // আয়ে যোগ করি
                    window.parent.moneyManager.data.income.push(testData);
                    window.parent.moneyManager.saveData();

                    console.log('Quick test data added:', testData);

                    // এডিট মডাল খুলি
                    setTimeout(() => {
                        console.log('Opening edit modal for quick test...');
                        window.parent.moneyManager.editTransaction(testData.id, 'income');

                        // ক্যাটেগরি চেক করি
                        setTimeout(() => {
                            const categorySelect = window.parent.document.getElementById('editCategory');
                            const selectedValue = categorySelect ? categorySelect.value : null;
                            const selectedText = categorySelect && categorySelect.selectedIndex >= 0
                                ? categorySelect.options[categorySelect.selectedIndex].textContent
                                : 'none';

                            console.log('Quick test results:');
                            console.log('- Expected category:', testData.category);
                            console.log('- Selected value:', selectedValue);
                            console.log('- Selected text:', selectedText);
                            console.log('- Selected index:', categorySelect ? categorySelect.selectedIndex : 'none');

                            if (selectedValue === testData.category) {
                                resultDiv.innerHTML = `
                                    <div class="success">
                                        <p>✓ দ্রুত পরীক্ষা সফল!</p>
                                        <p><strong>নির্বাচিত ক্যাটেগরি:</strong> ${selectedText} (${selectedValue})</p>
                                        <p><strong>প্রত্যাশিত ক্যাটেগরি:</strong> ব্যবসা (business)</p>
                                    </div>
                                `;
                            } else {
                                resultDiv.innerHTML = `
                                    <div class="error">
                                        <p>✗ দ্রুত পরীক্ষা ব্যর্থ!</p>
                                        <p><strong>প্রত্যাশিত:</strong> business</p>
                                        <p><strong>পাওয়া:</strong> ${selectedValue || 'undefined'}</p>
                                        <p><strong>নির্বাচিত টেক্সট:</strong> ${selectedText}</p>
                                        <p><strong>সূচক:</strong> ${categorySelect ? categorySelect.selectedIndex : 'none'}</p>
                                        <p><em>ব্রাউজার কনসোল (F12) চেক করুন আরো বিস্তারিত তথ্যের জন্য।</em></p>
                                    </div>
                                `;
                            }
                        }, 800);
                    }, 200);
                } else {
                    resultDiv.innerHTML = '<p class="error">✗ মূল অ্যাপ্লিকেশন পাওয়া যায়নি।</p>';
                }
            } catch (error) {
                resultDiv.innerHTML = `<p class="error">✗ ত্রুটি: ${error.message}</p>`;
                console.error('Quick test error:', error);
            }
        }

        // সম্পূর্ণ ওয়ার্কফ্লো পরীক্ষা
        function testCompleteWorkflow() {
            const resultDiv = document.getElementById('workflow-test-result');
            resultDiv.innerHTML = '<p class="info">সম্পূর্ণ ওয়ার্কফ্লো পরীক্ষা চলছে...</p>';
            
            try {
                if (window.parent && window.parent.moneyManager) {
                    const steps = [];
                    
                    // ধাপ ১: আয় যোগ করি
                    const testIncome = {
                        id: 'workflow-test-' + Date.now(),
                        amount: 7500,
                        category: 'business',
                        description: 'ওয়ার্কফ্লো পরীক্ষা',
                        date: new Date().toISOString().split('T')[0],
                        time: new Date().toLocaleTimeString('bn-BD'),
                        createdAt: new Date().toISOString(),
                        updatedAt: new Date().toISOString()
                    };
                    
                    window.parent.moneyManager.data.income.push(testIncome);
                    window.parent.moneyManager.saveData();
                    steps.push('✓ আয় যোগ করা হয়েছে');
                    
                    // ধাপ ২: এডিট নোটিফিকেশন যোগ করি
                    setTimeout(() => {
                        window.parent.moneyManager.addEditNotification(testIncome, 'income');
                        steps.push('✓ এডিট নোটিফিকেশন যোগ করা হয়েছে');
                        
                        // ধাপ ৩: এডিট মডাল খুলি
                        setTimeout(() => {
                            window.parent.moneyManager.editTransaction(testIncome.id, 'income');
                            steps.push('✓ এডিট মডাল খোলা হয়েছে');
                            
                            // ধাপ ৪: ফলাফল দেখাই
                            setTimeout(() => {
                                const categorySelect = window.parent.document.getElementById('editCategory');
                                if (categorySelect && categorySelect.value === 'business') {
                                    steps.push('✓ এডিট মডালে সঠিক ক্যাটেগরি নির্বাচিত');
                                } else {
                                    steps.push('✗ এডিট মডালে ক্যাটেগরি সমস্যা');
                                }
                                
                                resultDiv.innerHTML = `
                                    <div class="success">
                                        <h4>সম্পূর্ণ ওয়ার্কফ্লো পরীক্ষা সম্পন্ন:</h4>
                                        <ul>
                                            ${steps.map(step => `<li>${step}</li>`).join('')}
                                        </ul>
                                        <p><strong>নোটিফিকেশন প্যানেল চেক করুন ট্রান্সজিকশন আইডি দেখার জন্য।</strong></p>
                                    </div>
                                `;
                            }, 500);
                        }, 300);
                    }, 200);
                } else {
                    resultDiv.innerHTML = '<p class="error">✗ মূল অ্যাপ্লিকেশন পাওয়া যায়নি।</p>';
                }
            } catch (error) {
                resultDiv.innerHTML = `<p class="error">✗ ত্রুটি: ${error.message}</p>`;
            }
        }
    </script>
</body>
</html>
