<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>সমস্যা সমাধান পরীক্ষা</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-title {
            color: #2c3e50;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
            margin-bottom: 15px;
        }
        .test-button {
            background: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #2980b9;
        }
        .success {
            color: #27ae60;
            font-weight: bold;
        }
        .error {
            color: #e74c3c;
            font-weight: bold;
        }
        .info {
            color: #3498db;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <h1>নোটিফিকেশন এবং এডিট মডাল সমস্যা সমাধান পরীক্ষা</h1>
    
    <div class="test-container">
        <h2 class="test-title">১. নোটিফিকেশন প্যানেলে ট্রান্সজিকশন আইডি পরীক্ষা</h2>
        <p>এই পরীক্ষাটি নোটিফিকেশন প্যানেলে ট্রান্সজিকশন আইডি দেখাচ্ছে কিনা তা যাচাই করবে।</p>
        <button class="test-button" onclick="testNotificationWithTransactionId()">নোটিফিকেশন আইডি পরীক্ষা</button>
        <div id="notification-test-result"></div>
    </div>

    <div class="test-container">
        <h2 class="test-title">২. এডিট মডালে ক্যাটেগরি সিলেকশন পরীক্ষা</h2>
        <p>এই পরীক্ষাটি এডিট মডালে সঠিক ক্যাটেগরি নির্বাচিত হচ্ছে কিনা তা যাচাই করবে।</p>
        <button class="test-button" onclick="testEditModalCategory()">এডিট মডাল ক্যাটেগরি পরীক্ষা</button>
        <div id="edit-modal-test-result"></div>
    </div>

    <div class="test-container">
        <h2 class="test-title">৩. সম্পূর্ণ ওয়ার্কফ্লো পরীক্ষা</h2>
        <p>এই পরীক্ষাটি সম্পূর্ণ ওয়ার্কফ্লো (আয় যোগ → এডিট → নোটিফিকেশন) পরীক্ষা করবে।</p>
        <button class="test-button" onclick="testCompleteWorkflow()">সম্পূর্ণ ওয়ার্কফ্লো পরীক্ষা</button>
        <div id="workflow-test-result"></div>
    </div>

    <script>
        // নোটিফিকেশন আইডি পরীক্ষা
        function testNotificationWithTransactionId() {
            const resultDiv = document.getElementById('notification-test-result');
            resultDiv.innerHTML = '<p class="info">পরীক্ষা চলছে...</p>';
            
            try {
                // মূল অ্যাপ্লিকেশনে নোটিফিকেশন যোগ করার চেষ্টা
                if (window.parent && window.parent.moneyManager) {
                    const testNotification = {
                        title: 'পরীক্ষা নোটিফিকেশন',
                        message: 'এটি একটি পরীক্ষা নোটিফিকেশন যাতে ট্রান্সজিকশন আইডি রয়েছে',
                        type: 'transaction',
                        category: 'transactions',
                        actionData: {
                            transactionId: 'test-id-12345',
                            transactionType: 'income',
                            action: 'edit'
                        }
                    };
                    
                    window.parent.moneyManager.addNotification(
                        testNotification.title,
                        testNotification.message,
                        testNotification.type,
                        testNotification.category,
                        testNotification.actionData
                    );
                    
                    resultDiv.innerHTML = '<p class="success">✓ নোটিফিকেশন সফলভাবে যোগ করা হয়েছে। নোটিফিকেশন প্যানেল চেক করুন।</p>';
                } else {
                    resultDiv.innerHTML = '<p class="error">✗ মূল অ্যাপ্লিকেশন পাওয়া যায়নি। index.html থেকে এই পরীক্ষা চালান।</p>';
                }
            } catch (error) {
                resultDiv.innerHTML = `<p class="error">✗ ত্রুটি: ${error.message}</p>`;
            }
        }

        // এডিট মডাল ক্যাটেগরি পরীক্ষা
        function testEditModalCategory() {
            const resultDiv = document.getElementById('edit-modal-test-result');
            resultDiv.innerHTML = '<p class="info">পরীক্ষা চলছে...</p>';
            
            try {
                if (window.parent && window.parent.moneyManager) {
                    // একটি টেস্ট আয় যোগ করি
                    const testIncome = {
                        id: 'test-income-' + Date.now(),
                        amount: 5000,
                        category: 'salary',
                        description: 'পরীক্ষা আয়',
                        date: new Date().toISOString().split('T')[0],
                        time: new Date().toLocaleTimeString('bn-BD'),
                        createdAt: new Date().toISOString(),
                        updatedAt: new Date().toISOString()
                    };
                    
                    // ডেটায় যোগ করি
                    window.parent.moneyManager.data.income.push(testIncome);
                    window.parent.moneyManager.saveData();
                    
                    // এডিট মডাল খুলি
                    setTimeout(() => {
                        window.parent.moneyManager.editTransaction(testIncome.id, 'income');
                        
                        // ক্যাটেগরি চেক করি
                        setTimeout(() => {
                            const categorySelect = window.parent.document.getElementById('editCategory');
                            if (categorySelect && categorySelect.value === 'salary') {
                                resultDiv.innerHTML = '<p class="success">✓ এডিট মডালে সঠিক ক্যাটেগরি নির্বাচিত হয়েছে।</p>';
                            } else {
                                resultDiv.innerHTML = `<p class="error">✗ ক্যাটেগরি সঠিক নয়। প্রত্যাশিত: salary, পাওয়া: ${categorySelect ? categorySelect.value : 'undefined'}</p>`;
                            }
                        }, 500);
                    }, 100);
                } else {
                    resultDiv.innerHTML = '<p class="error">✗ মূল অ্যাপ্লিকেশন পাওয়া যায়নি।</p>';
                }
            } catch (error) {
                resultDiv.innerHTML = `<p class="error">✗ ত্রুটি: ${error.message}</p>`;
            }
        }

        // সম্পূর্ণ ওয়ার্কফ্লো পরীক্ষা
        function testCompleteWorkflow() {
            const resultDiv = document.getElementById('workflow-test-result');
            resultDiv.innerHTML = '<p class="info">সম্পূর্ণ ওয়ার্কফ্লো পরীক্ষা চলছে...</p>';
            
            try {
                if (window.parent && window.parent.moneyManager) {
                    const steps = [];
                    
                    // ধাপ ১: আয় যোগ করি
                    const testIncome = {
                        id: 'workflow-test-' + Date.now(),
                        amount: 7500,
                        category: 'business',
                        description: 'ওয়ার্কফ্লো পরীক্ষা',
                        date: new Date().toISOString().split('T')[0],
                        time: new Date().toLocaleTimeString('bn-BD'),
                        createdAt: new Date().toISOString(),
                        updatedAt: new Date().toISOString()
                    };
                    
                    window.parent.moneyManager.data.income.push(testIncome);
                    window.parent.moneyManager.saveData();
                    steps.push('✓ আয় যোগ করা হয়েছে');
                    
                    // ধাপ ২: এডিট নোটিফিকেশন যোগ করি
                    setTimeout(() => {
                        window.parent.moneyManager.addEditNotification(testIncome, 'income');
                        steps.push('✓ এডিট নোটিফিকেশন যোগ করা হয়েছে');
                        
                        // ধাপ ৩: এডিট মডাল খুলি
                        setTimeout(() => {
                            window.parent.moneyManager.editTransaction(testIncome.id, 'income');
                            steps.push('✓ এডিট মডাল খোলা হয়েছে');
                            
                            // ধাপ ৪: ফলাফল দেখাই
                            setTimeout(() => {
                                const categorySelect = window.parent.document.getElementById('editCategory');
                                if (categorySelect && categorySelect.value === 'business') {
                                    steps.push('✓ এডিট মডালে সঠিক ক্যাটেগরি নির্বাচিত');
                                } else {
                                    steps.push('✗ এডিট মডালে ক্যাটেগরি সমস্যা');
                                }
                                
                                resultDiv.innerHTML = `
                                    <div class="success">
                                        <h4>সম্পূর্ণ ওয়ার্কফ্লো পরীক্ষা সম্পন্ন:</h4>
                                        <ul>
                                            ${steps.map(step => `<li>${step}</li>`).join('')}
                                        </ul>
                                        <p><strong>নোটিফিকেশন প্যানেল চেক করুন ট্রান্সজিকশন আইডি দেখার জন্য।</strong></p>
                                    </div>
                                `;
                            }, 500);
                        }, 300);
                    }, 200);
                } else {
                    resultDiv.innerHTML = '<p class="error">✗ মূল অ্যাপ্লিকেশন পাওয়া যায়নি।</p>';
                }
            } catch (error) {
                resultDiv.innerHTML = `<p class="error">✗ ত্রুটি: ${error.message}</p>`;
            }
        }
    </script>
</body>
</html>
